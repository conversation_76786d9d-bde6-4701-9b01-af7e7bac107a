"""
Investment Thesis Models

This module defines the data models for investment theses, including scoring rules,
match rules, and thesis configuration.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Annotated, Any, Dict, ForwardRef, List, Literal, Optional, Union

from bson import ObjectId
from pydantic import BaseModel, Field, ValidationInfo, field_validator, model_validator

from app.models.base import TractionXModel
from app.models.form import QuestionType
from app.utils.common import ObjectIdField, PyObjectId
from app.utils.model.helper import populate_reference


class RuleType(str, Enum):
    """Types of scoring rules."""

    SCORING = "scoring"  # Regular scoring rule
    BONUS = "bonus"  # Bonus scoring rule


class AggregationType(str, Enum):
    """Aggregation types for repeatable section questions."""

    NONE = "none"  # No aggregation
    ANY = "any"  # Any item matches
    ALL = "all"  # All items match
    COUNT = "count"  # Count of matching items
    PERCENTAGE = "percentage"  # Percentage of matching items
    # AVG = "avg"  # Average of numeric values
    # SUM = "sum"  # Sum of numeric values
    # MIN = "min"  # Minimum of numeric values
    # MAX = "max"  # Maximum of numeric values
    # SUM_BY_FILTER_PERCENT_OF_TOTAL = (
    #     "sum_by_filter_percent_of_total"  # Special case for percentage calculations
    # )


class LogicalOperator(str, Enum):
    """Logical operators for compound conditions."""

    AND = "and"
    OR = "or"
    NOT = "not"


class ConditionOperator(str, Enum):
    """Operators for conditions in rules."""

    EQUALS = "eq"  # Equal to
    NOT_EQUALS = "ne"  # Not equal to
    GREATER_THAN = "gt"  # Greater than
    LESS_THAN = "lt"  # Less than
    GREATER_THAN_EQUALS = "gte"  # Greater than or equal to
    LESS_THAN_EQUALS = "lte"  # Less than or equal to
    CONTAINS = "contains"  # Contains (for strings and lists)
    NOT_CONTAINS = "not_contains"  # Does not contain
    STARTS_WITH = "starts_with"  # Starts with
    ENDS_WITH = "ends_with"  # Ends with
    IN = "in"  # In list
    NOT_IN = "not_in"  # Not in list
    BETWEEN = "between"  # Between two values
    NOT_BETWEEN = "not_between"  # Not between two values


class FilterCondition(BaseModel):
    """Represents a single question condition."""

    question_id: str  # The ID of the question to check
    operator: Optional[ConditionOperator] = None  # Operator for comparison
    value: Any  # Value to compare against


# CompoundCondition is recursive, needs a forward reference
CompoundCondition = ForwardRef("CompoundCondition")  # type: ignore


class CompoundCondition(BaseModel):  # noqa: F811
    """Represents a logical combination of conditions."""

    operator: LogicalOperator
    conditions: List[Union[FilterCondition, "CompoundCondition"]]


# Patch recursive type
CompoundCondition.update_forward_refs()


class ScoringRule(TractionXModel):
    """
    Unified scoring rule for both scoring and bonus logic.
    Supports both simple and compound conditions, with aggregation for repeatable sections.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    thesis_id: ObjectIdField  # Reference to the thesis

    # Core fields
    rule_type: RuleType  # SCORING or BONUS
    question_id: Optional[Annotated[ObjectIdField, populate_reference("Question")]] = (
        None
    )
    question_type: Optional[QuestionType] = None
    weight: float = 1.0  # Weight (SCORING only)
    bonus_points: Optional[float] = None  # Points for BONUS rules

    # Condition logic
    condition: Union[FilterCondition, CompoundCondition]  # The condition to match

    # Repeatable section support
    section_id: Optional[Annotated[ObjectIdField, populate_reference("Section")]] = None
    aggregation: Optional[AggregationType] = None
    filter: Optional[FilterCondition] = None
    value_field: Optional[int] = None
    aggregate_operator: Optional[ConditionOperator] = None
    aggregate_threshold: Optional[float] = None

    # Metadata
    notes: Optional[str] = None
    is_deleted: bool = False
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )

    @field_validator("condition", mode="before")
    @classmethod
    def convert_condition_object_ids(cls, v):
        """Convert ObjectId fields to strings in condition objects."""
        if isinstance(v, dict):
            # Handle FilterCondition
            if "question_id" in v and isinstance(v["question_id"], ObjectId):
                v = v.copy()  # Don't modify the original
                v["question_id"] = str(v["question_id"])

            # Handle CompoundCondition
            if "conditions" in v and isinstance(v["conditions"], list):
                v = v.copy()  # Don't modify the original
                converted_conditions = []
                for condition in v["conditions"]:
                    if isinstance(condition, dict):
                        if "question_id" in condition and isinstance(
                            condition["question_id"], ObjectId
                        ):
                            condition = condition.copy()
                            condition["question_id"] = str(condition["question_id"])
                        # Recursively handle nested compound conditions
                        if "conditions" in condition:
                            condition = cls.convert_condition_object_ids(condition)
                    converted_conditions.append(condition)
                v["conditions"] = converted_conditions

        return v

    @field_validator("filter", mode="before")
    @classmethod
    def convert_filter_object_ids(cls, v):
        """Convert ObjectId fields to strings in filter objects."""
        if (
            isinstance(v, dict)
            and "question_id" in v
            and isinstance(v["question_id"], ObjectId)
        ):
            v = v.copy()  # Don't modify the original
            v["question_id"] = str(v["question_id"])
        return v

    @field_validator("bonus_points")
    @classmethod
    def validate_bonus_points(
        cls, v: Optional[float], info: ValidationInfo
    ) -> Optional[float]:
        """Validate that bonus_points is set if rule_type is BONUS."""
        if info.data.get("rule_type") == RuleType.BONUS and v is None:
            raise ValueError("bonus_points is required for bonus rules")
        return v

    @field_validator("weight")
    @classmethod
    def validate_weight(cls, v: float, info: ValidationInfo) -> float:
        """Validate that weight is positive and only used for SCORING rules."""
        if info.data.get("rule_type") == RuleType.BONUS and v != 1.0:
            raise ValueError("weight should be 1.0 for bonus rules")
        if v <= 0:
            raise ValueError("weight must be positive")
        return v

    @model_validator(mode="before")
    def validate_rule_fields(cls, values):
        """Validate rule-specific field combinations."""
        # Skip validation if this appears to be during serialization or update (has enum objects)

        # Check if any field contains enum objects (indicates serialization/existing data)
        def has_enum_objects(obj):
            """Recursively check if an object contains enum instances."""
            if hasattr(obj, "value") and hasattr(obj, "name"):  # Enum object
                return True
            elif isinstance(obj, dict):
                return any(has_enum_objects(v) for v in obj.values())
            elif isinstance(obj, list):
                return any(has_enum_objects(item) for item in obj)
            return False

        # If any field has enum objects, skip validation (this is serialization or existing data)
        if has_enum_objects(values):
            return values

        # Check for specific fields that might have ObjectId instances (also indicates existing data)
        if isinstance(values.get("_id"), ObjectId) or isinstance(
            values.get("thesis_id"), ObjectId
        ):
            return values

        rule_type = values.get("rule_type")
        condition = values.get("condition")
        question_id = values.get("question_id")
        aggregation = values.get("aggregation")

        if not condition:
            raise ValueError("condition is required for all rules")

        if rule_type == RuleType.SCORING.value or rule_type == "scoring":
            if not question_id:
                raise ValueError("SCORING rules must have a question_id")
            if values.get("bonus_points") is not None:
                raise ValueError("SCORING rules cannot have bonus_points")

        if rule_type == RuleType.BONUS.value or rule_type == "bonus":
            if question_id and not isinstance(condition, (CompoundCondition, dict)):
                raise ValueError(
                    "BONUS rules with question_id must use compound conditions"
                )

        # Validate aggregation fields if present
        if (
            aggregation
            and aggregation != AggregationType.NONE.value
            and aggregation != "none"
        ):
            if not values.get("section_id"):
                raise ValueError("aggregation requires section_id")
            if not values.get("value_field"):
                raise ValueError("aggregation requires value_field")

        return values


class MatchRule(TractionXModel):
    """
    Represents a match rule for a thesis.

    Match rules determine which form responses a thesis applies to.
    They are structured as a condition tree with logical operators.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    thesis_id: ObjectIdField  # Reference to the thesis
    name: str  # Name of the rule
    description: Optional[str] = None  # Description of the rule

    # Condition tree structure
    operator: Literal["and", "or"] = "and"  # Logical operator for conditions
    conditions: List[Dict[str, Any]] = Field(default_factory=list)  # List of conditions
    is_deleted: bool = False  # Whether this rule is deleted

    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )


class ThesisStatus(str, Enum):
    """Status of a thesis."""

    DRAFT = "draft"  # Draft thesis
    ACTIVE = "active"  # Active thesis
    ARCHIVED = "archived"  # Archived thesis


class InvestmentThesis(TractionXModel):
    """
    Represents an investment thesis.

    An investment thesis defines an investor's intent, target, and evaluation logic.
    It is tied to a specific form and includes both match rules and scoring rules.
    """

    id: ObjectIdField = Field(default_factory=PyObjectId, alias="_id")
    org_id: ObjectIdField  # Organization that owns this thesis
    name: str  # Name of the thesis
    description: str  # Description of the thesis
    form_id: Annotated[
        ObjectIdField, populate_reference("Form")
    ]  # Form this thesis applies to

    # Status and metadata
    status: ThesisStatus = ThesisStatus.DRAFT  # Status of the thesis
    is_active: bool = True  # Whether this thesis is active
    is_deleted: bool = False  # Whether this thesis is deleted

    # References to rules
    scoring_rules: List[Annotated[ObjectIdField, populate_reference("ScoringRule")]] = (
        Field(default_factory=list)
    )  # References to scoring rules
    match_rules: List[Annotated[ObjectIdField, populate_reference("MatchRule")]] = (
        Field(default_factory=list)
    )  # References to match rules

    # Timestamps
    created_by: ObjectIdField  # User who created this thesis
    created_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )
    updated_at: int = Field(
        default_factory=lambda: int(datetime.now(timezone.utc).timestamp())
    )

    # Optional fields for workflow
    submitted_at: Optional[int] = None  # When this thesis was submitted
    reviewed_at: Optional[int] = None  # When this thesis was reviewed


class ThesisWithRules(InvestmentThesis):
    """
    Represents an investment thesis with its rules expanded.

    This model is used for API responses and includes the full rule objects
    instead of just references.
    """

    scoring_rules: List[ScoringRule] = Field(default_factory=list)
    match_rules: List[MatchRule] = Field(default_factory=list)

    @model_validator(mode="after")  # type: ignore
    def ensure_rules_are_objects(self) -> "ThesisWithRules":
        """Ensure that scoring_rules and match_rules are lists of objects, not IDs."""
        if not isinstance(self.scoring_rules, list) or any(
            isinstance(rule, ObjectId) for rule in self.scoring_rules
        ):
            self.scoring_rules = []

        if not isinstance(self.match_rules, list) or any(
            isinstance(rule, ObjectId) for rule in self.match_rules
        ):
            self.match_rules = []

        return self
