"use client"

import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { Plus, Star, Trash2, Info, Save, X, Loader2, Edit } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { BonusConditionBuilder } from './bonus-condition-builder';
import { BonusRule, CompoundCondition, RuleType, LogicalOperator } from '@/lib/types/thesis';
import { Form } from '@/lib/types/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';

// Use thesis types for consistency

interface BonusBlocksEditorProps {
  bonusRules: Partial<BonusRule>[];
  onAddBonusRule: (rule: Partial<BonusRule>) => void;
  onUpdateBonusRule: (index: number, rule: Partial<BonusRule>) => void;
  onRemoveBonusRule: (index: number) => void;
  form?: Form;
  onDeleteBonusRule?: (index: number, rule: Partial<BonusRule>) => Promise<void>;
  onCreateBonusRule?: (rule: Partial<BonusRule>) => Promise<void>;
  onSaveBonusRule?: (ruleId: string, rule: Partial<BonusRule>) => Promise<void>;
}

// Helper component for condition building - memoized to prevent recreation
const BonusConditionWrapper = React.memo(({
  condition,
  onChange,
  form,
  ruleIndex,
  disabled = false
}: {
  condition?: CompoundCondition;
  onChange: (condition: CompoundCondition) => void;
  form: Form;
  ruleIndex: number;
  disabled?: boolean;
}) => {
  // Memoize questions to prevent unnecessary recalculations
  const allQuestions = useMemo(() => {
    if (!form?.sections) return [];

    return form.sections.flatMap(section =>
      section.questions?.map(q => ({
        _id: q._id,
        id: q.id,
        label: q.label,
        type: q.type,
        section_title: section.title,
        options: q.options
      })) || []
    );
  }, [form]);

  // Convert single CompoundCondition to array format for ConditionBuilder
  const conditions = useMemo(() => condition?.conditions || [], [condition?.conditions]);

  const handleConditionsChange = useCallback((newConditions: any[]) => {
    try {
      const updatedCondition: CompoundCondition = {
        operator: (condition?.operator || LogicalOperator.AND) as LogicalOperator,
        conditions: newConditions
      };
      onChange(updatedCondition);
    } catch (error) {
      console.error('Error updating conditions for bonus rule', ruleIndex, error);
    }
  }, [condition?.operator, onChange, ruleIndex]);

  const handleOperatorChange = useCallback((value: string) => {
    try {
      const updatedCondition: CompoundCondition = {
        operator: value as LogicalOperator,
        conditions: condition?.conditions || []
      };
      onChange(updatedCondition);
    } catch (error) {
      console.error('Error updating operator for bonus rule', ruleIndex, error);
    }
  }, [condition?.conditions, onChange, ruleIndex]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <label className="text-sm font-medium">Logic:</label>
        <Select
          value={condition?.operator || LogicalOperator.AND}
          onValueChange={handleOperatorChange}
          disabled={disabled}
        >
          <SelectTrigger className="w-24">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={LogicalOperator.AND}>ALL</SelectItem>
            <SelectItem value={LogicalOperator.OR}>ANY</SelectItem>
          </SelectContent>
        </Select>
        <span className="text-sm text-muted-foreground">
          of the following conditions must be met
        </span>
      </div>

      <BonusConditionBuilder
        conditions={conditions as any[]}
        onConditionsChange={handleConditionsChange}
        allQuestions={allQuestions}
        form={form}
        readOnly={disabled}
      />
    </div>
  );
});

BonusConditionWrapper.displayName = 'BonusConditionWrapper';

// Individual bonus rule card component - enhanced with Edit/Save functionality
const BonusRuleCard = React.memo(({
  rule,
  index,
  onUpdate,
  onRemove,
  onSave,
  onCancel,
  form,
  onDeleteBonusRule,
  isNew = false
}: {
  rule: Partial<BonusRule>;
  index: number;
  onUpdate: (updates: Partial<BonusRule>) => void;
  onRemove: () => void;
  onSave?: () => void;
  onCancel?: () => void;
  form?: Form;
  onDeleteBonusRule?: (index: number, rule: Partial<BonusRule>) => Promise<void>;
  isNew?: boolean;
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(isNew); // New rules start in edit mode
  const [localBonusPoints, setLocalBonusPoints] = useState(rule.bonus_points?.toString() || '1');

  // Simple, reliable dirty state detection
  const isDirty = isNew || isEditing;
  const ruleId = rule._id || rule.id;

  // Update local state when rule changes from external source
  React.useEffect(() => {
    setLocalBonusPoints(rule.bonus_points?.toString() || '1');
  }, [rule.bonus_points]);

  const handleBonusPointsChange = useCallback((value: string) => {
    // Update local state immediately for responsive UI
    setLocalBonusPoints(value);
    
    // Update the rule immediately for local state
    const numValue = Number(value);
    if (value !== '' && numValue > 0) {
      onUpdate({
        bonus_points: numValue
      });
    }
  }, [onUpdate]);

  const handleBonusPointsBlur = useCallback(() => {
    // On blur, ensure we have a valid value
    const numValue = Number(localBonusPoints);
    if (localBonusPoints === '' || numValue <= 0) {
      // Reset to minimum valid value
      setLocalBonusPoints('1');
      onUpdate({
        bonus_points: 1
      });
    }
  }, [localBonusPoints, onUpdate]);

  const handleConditionChange = useCallback((condition: CompoundCondition) => {
    onUpdate({
      compound_condition: condition
    });
  }, [onUpdate]);

  const handleEdit = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
    // Note: We're not resetting the form data here as it would require 
    // storing original values. The parent component should handle this if needed.
  }, []);

  const handleSave = useCallback(async () => {
    // Validate before saving
    if (!rule.bonus_points || rule.bonus_points <= 0) {
      toast({
        title: "Invalid Bonus Points",
        description: "Bonus points must be a positive number.",
        variant: "destructive",
      });
      return;
    }

    if (!rule.compound_condition?.conditions || rule.compound_condition.conditions.length === 0) {
      toast({
        title: "Missing Conditions",
        description: "Please add at least one condition for the bonus rule.",
        variant: "destructive",
      });
      return;
    }

    if (onSave) {
      try {
        setIsSaving(true);
        onSave();
        setIsEditing(false);
        toast({
          title: "Success",
          description: "Bonus rule saved successfully.",
        });
      } catch (error) {
        console.error('Error saving bonus rule:', error);
        toast({
          title: "Error",
          description: "Failed to save bonus rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    }
  }, [rule, onSave]);

  const handleRemove = useCallback(async () => {
    if (!isNew && onDeleteBonusRule) {
      try {
        setIsDeleting(true);
        await onDeleteBonusRule(index, rule);
      } catch (error) {
        console.error('Error deleting bonus rule via API:', error);
      } finally {
        setIsDeleting(false);
      }
    }
    onRemove();
  }, [onDeleteBonusRule, index, rule, onRemove, isNew]);

  const handleCancel = useCallback(() => {
    if (onCancel) {
      onCancel();
    } else {
      setIsEditing(false);
    }
  }, [onCancel]);



  const hasValidConditions = rule.compound_condition?.conditions && rule.compound_condition.conditions.length > 0;
  const hasValidPoints = rule.bonus_points && rule.bonus_points > 0;
  const canSave = hasValidConditions && hasValidPoints;

  return (
    <Card className={`border-l-4 ${isDirty ? 'border-l-green-500 bg-green-50/30' : 'border-l-green-300 bg-green-50/50'}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Save Button - Show if dirty (new rule or editing) */}
            {isDirty && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSave}
                disabled={!canSave || isSaving}
                className="text-green-600 hover:text-green-700 hover:bg-green-50"
                type="button"
                title="Save Bonus Rule"
              >
                {isSaving ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Save className="size-4" />
                )}
              </Button>
            )}

            {/* Cancel Button - Show when editing existing rule */}
            {isEditing && ruleId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancelEdit}
                className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                type="button"
                title="Cancel Edit"
              >
                Cancel
              </Button>
            )}

            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-green-600" />
              <Badge variant={isDirty ? "secondary" : "outline"} className="text-xs">
                {isDirty && !ruleId ? "New Bonus Rule" : `Bonus Rule #${index + 1}`}
              </Badge>
            </div>
            
            <div className="text-sm text-muted-foreground">
              {rule.bonus_points || 0} points
              {isDirty && <span className="text-green-600 ml-2">• {ruleId ? "Editing" : "Unsaved"}</span>}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Edit Button - Only show for saved rules that aren't being edited */}
            {ruleId && !isDirty && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                type="button"
                title="Edit Bonus Rule"
              >
                <Edit className="size-4" />
              </Button>
            )}

            {/* Delete Button - Only show for saved rules */}
            {ruleId && onDeleteBonusRule && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemove}
                disabled={isDeleting}
                className="text-destructive hover:text-destructive"
                type="button"
                title="Delete Bonus Rule"
              >
                {isDeleting ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Trash2 className="size-4" />
                )}
              </Button>
            )}

            {/* Remove Button - Only show for unsaved rules */}
            {!ruleId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                className="text-destructive hover:text-destructive"
                type="button"
                title="Remove Bonus Rule"
              >
                <X className="size-4"/>
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Bonus points input */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium min-w-fit">Bonus Points:</label>
          <Input
            type="number"
            min="0"
            step="0.1"
            value={localBonusPoints}
            onChange={(e) => handleBonusPointsChange(e.target.value)}
            onBlur={handleBonusPointsBlur}
            className="w-24"
            placeholder="1"
            disabled={(!isDirty || isSaving || isDeleting) && !isEditing}
            readOnly={!isDirty && !isEditing}
          />
          <span className="text-sm text-muted-foreground">
            points awarded when conditions are met
          </span>
        </div>

        {/* Condition builder */}
        {form ? (
          <BonusConditionWrapper
            condition={rule.compound_condition}
            onChange={handleConditionChange}
            form={form}
            ruleIndex={index}
            disabled={(!isDirty || isSaving || isDeleting) && !isEditing}
          />
        ) : (
          <div className="text-center py-6 text-muted-foreground bg-muted/50 rounded-lg">
            <Star className="h-6 w-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Select a form to define bonus conditions</p>
          </div>
        )}

        {/* Validation messages for dirty rules */}
        {isDirty && (
          <div className="space-y-2">
            {!hasValidPoints && (
              <div className="text-sm text-green-600 bg-green-50 p-3 rounded-lg border border-green-200">
                <p className="font-medium">💡 Set bonus points</p>
                <p className="text-xs mt-1">Please set a positive number of bonus points.</p>
              </div>
            )}
            {!hasValidConditions && (
              <div className="text-sm text-green-600 bg-green-50 p-3 rounded-lg border border-green-200">
                <p className="font-medium">💡 Add conditions</p>
                <p className="text-xs mt-1">Please add at least one condition to define when this bonus applies.</p>
              </div>
            )}
            {!canSave && (
              <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg border border-amber-200">
                <p className="font-medium">Cannot save rule</p>
                <ul className="text-xs mt-1 space-y-1">
                  {!hasValidPoints && <li>• Bonus points must be a positive number</li>}
                  {!hasValidConditions && <li>• At least one condition is required</li>}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Rule preview */}
        {hasValidConditions && (
          <div className="bg-background border rounded-lg p-3">
            <div className="text-sm">
              <span className="font-medium">Rule Summary:</span>
              <span className="ml-2">
                Award <strong>{rule.bonus_points || 0} points</strong> when{' '}
                <strong>
                  {rule.compound_condition?.operator === 'and' ? 'ALL' :
                   rule.compound_condition?.operator === 'or' ? 'ANY' : 'NONE'}
                </strong>
                {' '}of {(rule.compound_condition?.conditions || []).length} condition(s) are met.
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

BonusRuleCard.displayName = 'BonusRuleCard';

export function BonusBlocksEditor({
  bonusRules,
  onAddBonusRule,
  onUpdateBonusRule,
  onRemoveBonusRule,
  form,
  onDeleteBonusRule,
  onCreateBonusRule,
  onSaveBonusRule
}: BonusBlocksEditorProps) {
  // Local state for new bonus rules being created
  const [newBonusRules, setNewBonusRules] = useState<Partial<BonusRule>[]>([]);
  const [isSavingAll, setIsSavingAll] = useState(false);

  // Memoize total bonus points calculation (only saved rules)
  const totalBonusPoints = useMemo(() => {
    if (!Array.isArray(bonusRules)) return 0;
    return bonusRules.reduce((total, rule) => total + (rule?.bonus_points || 0), 0);
  }, [bonusRules]);

  // Check if there are any unsaved bonus rules
  const hasUnsavedRules = newBonusRules.length > 0;

  // Save all new bonus rules
  const handleSaveAllBonusRules = useCallback(async () => {
    if (newBonusRules.length === 0) {
      toast({
        title: "No changes to save",
        description: "All bonus rules are already saved.",
      });
      return;
    }

    setIsSavingAll(true);
    let savedCount = 0;
    let errorCount = 0;

    try {
      for (const rule of newBonusRules) {
        try {
          // Validate the rule before saving
          if (!rule.bonus_points || rule.bonus_points <= 0) {
            console.warn('Skipping invalid bonus rule (no points):', rule);
            errorCount++;
            continue;
          }

          if (!rule.compound_condition?.conditions || rule.compound_condition.conditions.length === 0) {
            console.warn('Skipping invalid bonus rule (no conditions):', rule);
            errorCount++;
            continue;
          }

          if (onCreateBonusRule) {
            await onCreateBonusRule(rule);
            savedCount++;
          }
        } catch (error) {
          console.error('Error saving bonus rule:', error);
          errorCount++;
        }
      }

      if (savedCount > 0) {
        // Clear saved rules from local state
        setNewBonusRules([]);
        
        toast({
          title: "Bonus rules saved",
          description: `Successfully saved ${savedCount} bonus rule(s).${errorCount > 0 ? ` ${errorCount} failed to save.` : ''}`,
          variant: errorCount > 0 ? "destructive" : "default",
        });
      }
    } catch (error) {
      console.error('Error in save all operation:', error);
      toast({
        title: "Error saving bonus rules",
        description: "Failed to save bonus rules. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSavingAll(false);
    }
  }, [newBonusRules, onCreateBonusRule]);

  // Handle adding a new bonus rule (local state only)
  const handleAddBonusRule = useCallback(() => {
    try {
      const newBonusRule: Partial<BonusRule> = {
        rule_type: RuleType.BONUS,
        bonus_points: 1,
        compound_condition: {
          operator: LogicalOperator.AND,
          conditions: []
        },
        condition: {
          operator: LogicalOperator.AND,
          conditions: []
        },
        is_deleted: false
      };

      setNewBonusRules(prev => [...prev, newBonusRule]);
    } catch (error) {
      console.error('Error adding bonus rule:', error);
      toast({
        title: "Error",
        description: "Failed to add bonus rule. Please try again.",
        variant: "destructive",
      });
    }
  }, []);

  // Handle updating a saved bonus rule
  const handleUpdateBonusRule = useCallback((index: number) => {
    return (updates: Partial<BonusRule>) => {
      try {
        if (index < 0 || !Array.isArray(bonusRules) || index >= bonusRules.length) {
          console.error('Invalid bonus rule index:', index, 'Array length:', bonusRules?.length);
          return;
        }

        onUpdateBonusRule(index, updates);
      } catch (error) {
        console.error('Error updating bonus rule:', error);
      }
    };
  }, [bonusRules, onUpdateBonusRule]);

  // Handle updating a new bonus rule (local state)
  const handleUpdateNewBonusRule = useCallback((index: number) => {
    return (updates: Partial<BonusRule>) => {
      try {
        setNewBonusRules(prev => {
          if (index < 0 || index >= prev.length) {
            console.error('Invalid new bonus rule index:', index, 'Array length:', prev.length);
            return prev;
          }

          const updated = [...prev];
          updated[index] = { ...updated[index], ...updates };
          return updated;
        });
      } catch (error) {
        console.error('Error updating new bonus rule:', error);
      }
    };
  }, []);

  // Handle saving a new bonus rule (API call)
  const handleSaveNewBonusRule = useCallback((index: number) => {
    return async () => {
      try {
        const rule = newBonusRules[index];
        if (!rule) return;

        if (onCreateBonusRule) {
          await onCreateBonusRule(rule);
          
          // Remove from local state after successful save
          setNewBonusRules(prev => prev.filter((_, i) => i !== index));
        } else {
          // Fallback to legacy method
          onAddBonusRule(rule);
          setNewBonusRules(prev => prev.filter((_, i) => i !== index));
        }
      } catch (error) {
        console.error('Error saving new bonus rule:', error);
        throw error; // Re-throw to let the component handle loading states
      }
    };
  }, [newBonusRules, onCreateBonusRule, onAddBonusRule]);

  // Handle saving an existing bonus rule (API call)
  const handleSaveExistingBonusRule = useCallback((index: number) => {
    return async () => {
      try {
        const rule = bonusRules[index];
        if (!rule) return;

        const ruleId = rule._id || rule.id;
        if (ruleId && onSaveBonusRule) {
          await onSaveBonusRule(ruleId, rule);
        } else {
          console.warn('Cannot save bonus rule without ID:', rule);
          throw new Error('Cannot save rule without valid ID');
        }
      } catch (error) {
        console.error('Error saving existing bonus rule:', error);
        throw error; // Re-throw to let the component handle loading states
      }
    };
  }, [bonusRules, onSaveBonusRule]);

  // Handle canceling a new bonus rule
  const handleCancelNewBonusRule = useCallback((index: number) => {
    return () => {
      setNewBonusRules(prev => prev.filter((_, i) => i !== index));
    };
  }, []);

  // Handle removing a saved bonus rule
  const handleRemoveBonusRule = useCallback((index: number) => {
    return () => {
      try {
        if (index < 0 || !Array.isArray(bonusRules) || index >= bonusRules.length) {
          console.error('Invalid bonus rule index for removal:', index, 'Array length:', bonusRules?.length);
          return;
        }

        onRemoveBonusRule(index);
      } catch (error) {
        console.error('Error removing bonus rule:', error);
      }
    };
  }, [bonusRules, onRemoveBonusRule]);

  // Ensure bonusRules is always an array
  const safeRules = useMemo(() => {
    return Array.isArray(bonusRules) ? bonusRules : [];
  }, [bonusRules]);

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Bonus Scoring Rules
              </CardTitle>
              <CardDescription>
                Add bonus points for deals that meet specific criteria. These are applied in addition to the base scoring.
              </CardDescription>
            </div>
            {hasUnsavedRules && (
              <Button
                onClick={handleSaveAllBonusRules}
                disabled={isSavingAll}
                className="flex items-center gap-2"
                type="button"
              >
                {isSavingAll ? (
                  <>
                    <Loader2 className="size-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="size-4" />
                    Save All Changes
                  </>
                )}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <Info className="h-4 w-4" />
            <AlertDescription>
              Bonus rules are independent conditions that award extra points.
              Each rule is evaluated separately and can stack with others.
            </AlertDescription>
          </Alert>

          {safeRules.length > 0 && (
            <div className="mb-4 p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Possible Bonus Points:</span>
                <Badge variant="secondary" className="text-lg px-3 py-1">
                  {totalBonusPoints}
                </Badge>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {/* Saved bonus rules */}
            {safeRules.map((rule, index) => (
              <BonusRuleCard
                key={rule.id || rule._id || `bonus-rule-${index}`}
                rule={rule}
                index={index}
                onUpdate={handleUpdateBonusRule(index)}
                onRemove={handleRemoveBonusRule(index)}
                onSave={handleSaveExistingBonusRule(index)}
                form={form}
                onDeleteBonusRule={onDeleteBonusRule}
                isNew={false}
              />
            ))}

            {/* New bonus rules (local state) */}
            {newBonusRules.map((rule, index) => (
              <BonusRuleCard
                key={`new-bonus-rule-${index}`}
                rule={rule}
                index={safeRules.length + index} // Offset index for display purposes
                onUpdate={handleUpdateNewBonusRule(index)}
                onRemove={handleCancelNewBonusRule(index)}
                onSave={handleSaveNewBonusRule(index)}
                onCancel={handleCancelNewBonusRule(index)}
                form={form}
                isNew={true}
              />
            ))}

            {safeRules.length === 0 && newBonusRules.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Star className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="font-medium">No bonus rules defined</p>
                <p className="text-sm">Add bonus rules to award extra points for exceptional deals.</p>
              </div>
            )}
          </div>

          <Button
            onClick={handleAddBonusRule}
            variant="outline"
            className="w-full mt-4"
            disabled={!form}
            type="button"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Bonus Rule
          </Button>

          {!form && (
            <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg mt-4">
              <p>Please select a form first to create bonus rules based on its questions.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bonus rules help */}
      <Card className="border-dashed">
        <CardContent className="pt-6">
          <div className="space-y-3">
            <h4 className="font-medium text-sm">💡 Bonus Rules Tips</h4>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>Bonus rules are evaluated independently of base scoring</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>Multiple bonus rules can apply to the same deal</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>Use bonus rules to reward exceptional characteristics</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="font-medium">•</span>
                <span>Consider the total possible bonus when setting point values</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
