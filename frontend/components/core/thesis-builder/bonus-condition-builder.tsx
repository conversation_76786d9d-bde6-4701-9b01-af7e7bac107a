"use client"

import React, { useState, use<PERSON>emo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, Info, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  FilterCondition, 
  CompoundFilter, 
  ConditionOperator, 
  LogicalOperator,
  AggregationType 
} from '@/lib/types/thesis';
import { Form, Section, Question } from '@/lib/types/form';

// Use thesis types for consistency
type ConditionItem = FilterCondition | CompoundFilter;

interface QuestionWithSection {
  _id?: string;
  id?: string;
  label: string;
  type: string;
  section_title: string;
  section_id?: string;
  options?: Array<{ label: string; value: string }>;
}

interface BonusConditionBuilderProps {
  conditions: ConditionItem[];
  onConditionsChange: (conditions: ConditionItem[]) => void;
  allQuestions: QuestionWithSection[];
  form?: Form;
  className?: string;
  readOnly?: boolean;
}

// Aggregation condition interface
interface AggregationCondition extends FilterCondition {
  section_id?: string;
  aggregation?: AggregationType;
  aggregate_threshold?: number;
  aggregate_operator?: ConditionOperator;
}

// Question types that support aggregation in repeatable sections
const AGGREGATION_QUESTION_TYPES = ['single_select', 'multi_select', 'boolean', 'number'];

// Operators by question type
const OPERATORS_BY_TYPE = {
  single_select: [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS, ConditionOperator.IN, ConditionOperator.NOT_IN],
  multi_select: [ConditionOperator.CONTAINS, ConditionOperator.NOT_CONTAINS, ConditionOperator.IN, ConditionOperator.NOT_IN],
  boolean: [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS],
  number: [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS, ConditionOperator.GREATER_THAN, ConditionOperator.LESS_THAN, ConditionOperator.GREATER_THAN_EQUALS, ConditionOperator.LESS_THAN_EQUALS, ConditionOperator.BETWEEN],
  short_text: [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS, ConditionOperator.CONTAINS, ConditionOperator.NOT_CONTAINS, ConditionOperator.STARTS_WITH, ConditionOperator.ENDS_WITH],
  long_text: [ConditionOperator.CONTAINS, ConditionOperator.NOT_CONTAINS]
};

// Helper functions
function isSimpleCondition(condition: ConditionItem): condition is FilterCondition {
  return 'question_id' in condition;
}

function isCompoundCondition(condition: ConditionItem): condition is CompoundFilter {
  return 'operator' in condition && 'conditions' in condition;
}

function isAggregationCondition(condition: FilterCondition): condition is AggregationCondition {
  return 'section_id' in condition && 'aggregation' in condition;
}

// Get aggregation explanation text
function getAggregationExplanation(
  aggregation: AggregationType, 
  threshold?: number, 
  questionLabel?: string,
  sectionTitle?: string,
  operator?: ConditionOperator
): string {
  const target = questionLabel ? `"${questionLabel}"` : 'the condition';
  const section = sectionTitle ? ` in ${sectionTitle}` : '';
  
  switch (aggregation) {
    case AggregationType.ANY:
      return `At least one item${section} must match ${target}`;
    case AggregationType.ALL:
      return `All items${section} must match ${target}`;
    case AggregationType.COUNT:
      return `At least ${threshold || 1} items${section} must match ${target}`;
    case AggregationType.PERCENTAGE:
      return `At least ${(threshold || 50)}% of items${section} must match ${target}`;
    case AggregationType.SUM:
      const operatorText = operator === ConditionOperator.GREATER_THAN_EQUALS ? '≥' :
                          operator === ConditionOperator.GREATER_THAN ? '>' :
                          operator === ConditionOperator.LESS_THAN_EQUALS ? '≤' :
                          operator === ConditionOperator.LESS_THAN ? '<' :
                          operator === ConditionOperator.EQUALS ? '=' :
                          operator === ConditionOperator.NOT_EQUALS ? '≠' : '≥';
      return `Total ${target}${section} ${operatorText} ${threshold || '?'}`;
    default:
      return `Items${section} will be evaluated individually`;
  }
}

// Aggregation condition block component
interface AggregationConditionBlockProps {
  condition: AggregationCondition;
  index: number;
  onUpdate: (updates: Partial<AggregationCondition>) => void;
  onRemove: () => void;
  allQuestions: QuestionWithSection[];
  form?: Form;
  readOnly?: boolean;
}

function AggregationConditionBlock({
  condition,
  index,
  onUpdate,
  onRemove,
  allQuestions,
  form,
  readOnly
}: AggregationConditionBlockProps) {
  // Get repeatable sections from form
  const repeatableSections = useMemo(() => {
    if (!form?.sections) return [];
    return form.sections.filter(section => section.repeatable);
  }, [form]);

  // Get questions for selected section
  const sectionQuestions = useMemo(() => {
    if (!condition.section_id || !form?.sections) return [];
    const section = form.sections.find(s => (s._id || s.id) === condition.section_id);
    if (!section) return [];
    return section.questions.filter(q => AGGREGATION_QUESTION_TYPES.includes(q.type));
  }, [condition.section_id, form]);

  const selectedQuestion = sectionQuestions.find(q => (q._id || q.id) === condition.question_id);
  const selectedSection = repeatableSections.find(s => (s._id || s.id) === condition.section_id);
  
  const availableOperators = selectedQuestion
    ? OPERATORS_BY_TYPE[selectedQuestion.type as keyof typeof OPERATORS_BY_TYPE] || []
    : [];

  const needsThreshold = condition.aggregation === AggregationType.COUNT || condition.aggregation === AggregationType.PERCENTAGE || condition.aggregation === AggregationType.SUM;

  return (
    <Card className="p-4 bg-gradient-to-r from-blue-50/50 to-purple-50/50 border-l-4 border-l-blue-500">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              Repeatable Section Rule
            </Badge>
            <HelpCircle className="h-4 w-4 text-muted-foreground" />
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="text-destructive hover:text-destructive"
            disabled={readOnly}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        {/* Section Selection */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Repeatable Section</Label>
            <Select
              value={condition.section_id || ''}
              onValueChange={(value) => onUpdate({ section_id: value, question_id: '', aggregation: AggregationType.ANY })}
              disabled={readOnly}
            >
              <SelectTrigger className={cn(!condition.section_id && "border-orange-200")}>
                <SelectValue placeholder="Select section..." />
              </SelectTrigger>
              <SelectContent>
                {repeatableSections.map((section) => (
                  <SelectItem key={section._id || section.id} value={section._id || section.id || ''}>
                    {section.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {!condition.section_id && (
              <p className="text-xs text-orange-600">Please select a repeatable section first</p>
            )}
          </div>

          {/* Question Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Question in Section</Label>
            <Select
              value={condition.question_id}
              onValueChange={(value) => onUpdate({ question_id: value })}
              disabled={readOnly || !condition.section_id}
            >
              <SelectTrigger className={cn(condition.section_id && !condition.question_id && "border-orange-200")}>
                <SelectValue placeholder={condition.section_id ? "Select question..." : "Select section first"} />
              </SelectTrigger>
              <SelectContent>
                {sectionQuestions.map((question) => (
                  <SelectItem key={question._id || question.id} value={question._id || question.id || ''}>
                    {question.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {condition.section_id && sectionQuestions.length === 0 && (
              <p className="text-xs text-orange-600">No compatible questions found in this section</p>
            )}
            {condition.section_id && !condition.question_id && sectionQuestions.length > 0 && (
              <p className="text-xs text-orange-600">Please select a question to evaluate</p>
            )}
          </div>
        </div>

        {/* Aggregation Type and Condition */}
        {condition.section_id && condition.question_id && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* Aggregation Type */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Aggregation Type</Label>
              <Select
                value={condition.aggregation || AggregationType.ANY}
                onValueChange={(value: AggregationType) => onUpdate({ aggregation: value })}
                disabled={readOnly}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={AggregationType.ANY}>
                    <div className="flex flex-col items-start">
                      <span>Any Match</span>
                      <span className="text-xs text-muted-foreground">At least one item matches</span>
                    </div>
                  </SelectItem>
                  <SelectItem value={AggregationType.ALL}>
                    <div className="flex flex-col items-start">
                      <span>All Match</span>
                      <span className="text-xs text-muted-foreground">Every item must match</span>
                    </div>
                  </SelectItem>
                  <SelectItem value={AggregationType.COUNT}>
                    <div className="flex flex-col items-start">
                      <span>Count</span>
                      <span className="text-xs text-muted-foreground">Minimum number of matches</span>
                    </div>
                  </SelectItem>
                  <SelectItem value={AggregationType.PERCENTAGE}>
                    <div className="flex flex-col items-start">
                      <span>Percentage</span>
                      <span className="text-xs text-muted-foreground">Minimum percentage of matches</span>
                    </div>
                  </SelectItem>
                  {selectedQuestion?.type === 'number' && (
                    <SelectItem value={AggregationType.SUM}>
                      <div className="flex flex-col items-start">
                        <span>Sum</span>
                        <span className="text-xs text-muted-foreground">Sum numeric values with threshold</span>
                      </div>
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Operator */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Operator</Label>
              <Select
                value={condition.operator || ''}
                onValueChange={(value: ConditionOperator) => onUpdate({ operator: value })}
                disabled={readOnly || !selectedQuestion}
              >
                <SelectTrigger className={cn(selectedQuestion && !condition.operator && "border-orange-200")}>
                  <SelectValue placeholder={selectedQuestion ? "Select operator..." : "Select question first"} />
                </SelectTrigger>
                <SelectContent>
                  {availableOperators.map((op) => (
                    <SelectItem key={op} value={op}>
                      {getOperatorDisplay(op)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedQuestion && !condition.operator && (
                <p className="text-xs text-orange-600">Please select how to compare the values</p>
              )}
            </div>

            {/* Value */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Value</Label>
              {selectedQuestion?.type === 'boolean' ? (
                <Select
                  value={condition.value?.toString() || ''}
                  onValueChange={(value) => onUpdate({ value: value === 'true' })}
                  disabled={readOnly}
                >
                  <SelectTrigger className={cn(condition.operator && condition.value === undefined && "border-orange-200")}>
                    <SelectValue placeholder="Select value..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Yes</SelectItem>
                    <SelectItem value="false">No</SelectItem>
                  </SelectContent>
                </Select>
              ) : selectedQuestion?.options ? (
                <Select
                  value={condition.value || ''}
                  onValueChange={(value) => onUpdate({ value })}
                  disabled={readOnly}
                >
                  <SelectTrigger className={cn(condition.operator && !condition.value && "border-orange-200")}>
                    <SelectValue placeholder="Select value..." />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedQuestion.options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Input
                  type={selectedQuestion?.type === 'number' ? 'number' : 'text'}
                  value={condition.value || ''}
                  onChange={(e) => onUpdate({ value: selectedQuestion?.type === 'number' ? Number(e.target.value) : e.target.value })}
                  placeholder="Enter value..."
                  disabled={readOnly}
                  className={cn(condition.operator && !condition.value && "border-orange-200")}
                />
              )}
              {condition.operator && !condition.value && (
                <p className="text-xs text-orange-600">Please enter a value to compare against</p>
              )}
            </div>
          </div>
        )}

        {/* Threshold Input */}
        {needsThreshold && (
          <div className="space-y-4">
            {condition.aggregation === AggregationType.SUM ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Sum Operator */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Comparison Operator</Label>
                  <Select
                    value={condition.aggregate_operator || ConditionOperator.GREATER_THAN_EQUALS}
                    onValueChange={(value: ConditionOperator) => onUpdate({ aggregate_operator: value })}
                    disabled={readOnly}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ConditionOperator.GREATER_THAN_EQUALS}>≥ (Greater than or equal)</SelectItem>
                      <SelectItem value={ConditionOperator.GREATER_THAN}>&gt; (Greater than)</SelectItem>
                      <SelectItem value={ConditionOperator.LESS_THAN_EQUALS}>≤ (Less than or equal)</SelectItem>
                      <SelectItem value={ConditionOperator.LESS_THAN}>&lt; (Less than)</SelectItem>
                      <SelectItem value={ConditionOperator.EQUALS}>= (Equals)</SelectItem>
                      <SelectItem value={ConditionOperator.NOT_EQUALS}>≠ (Not equals)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Sum Threshold */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Threshold Value</Label>
                  <Input
                    type="number"
                    value={condition.aggregate_threshold || ''}
                    onChange={(e) => onUpdate({ aggregate_threshold: Number(e.target.value) })}
                    placeholder="e.g. 80"
                    step="0.1"
                    disabled={readOnly}
                    className={cn(!condition.aggregate_threshold && "border-orange-200")}
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  {condition.aggregation === AggregationType.COUNT ? 'Minimum Count' : 'Minimum Percentage'}
                </Label>
                <Input
                  type="number"
                  value={condition.aggregate_threshold || ''}
                  onChange={(e) => onUpdate({ aggregate_threshold: Number(e.target.value) })}
                  placeholder={condition.aggregation === AggregationType.COUNT ? 'e.g. 2' : 'e.g. 50'}
                  min={condition.aggregation === AggregationType.PERCENTAGE ? 0 : 1}
                  max={condition.aggregation === AggregationType.PERCENTAGE ? 100 : undefined}
                  disabled={readOnly}
                  className={cn(!condition.aggregate_threshold && "border-orange-200")}
                />
              </div>
            )}
            
            <p className="text-xs text-muted-foreground">
              {condition.aggregation === AggregationType.COUNT
                ? 'Enter the minimum number of items that must match'
                : condition.aggregation === AggregationType.PERCENTAGE
                ? 'Enter the minimum percentage (0-100) of items that must match'
                : 'Enter the numeric threshold to compare the sum against'
              }
            </p>
            {!condition.aggregate_threshold && (
              <p className="text-xs text-orange-600">
                {condition.aggregation === AggregationType.COUNT
                  ? 'Please enter a minimum count (e.g., 2 for "at least 2 founders")'
                  : condition.aggregation === AggregationType.PERCENTAGE
                  ? 'Please enter a minimum percentage (e.g., 50 for "at least 50%")'
                  : 'Please enter a threshold value (e.g., 80 for "total equity ≥ 80%")'
                }
              </p>
            )}
          </div>
        )}

        {/* Explanation */}
        {condition.aggregation && condition.question_id && (
          <div className="p-3 bg-muted/50 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-muted-foreground">
                {getAggregationExplanation(
                  condition.aggregation,
                  condition.aggregate_threshold,
                  selectedQuestion?.label,
                  selectedSection?.title,
                  condition.aggregate_operator
                )}
              </p>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}

// Helper function to get operator display text
function getOperatorDisplay(operator: ConditionOperator): string {
  const operatorMap: Record<ConditionOperator, string> = {
    [ConditionOperator.EQUALS]: "Equals",
    [ConditionOperator.NOT_EQUALS]: "Not Equals",
    [ConditionOperator.GREATER_THAN]: "Greater Than",
    [ConditionOperator.LESS_THAN]: "Less Than",
    [ConditionOperator.GREATER_THAN_EQUALS]: "Greater Than or Equal",
    [ConditionOperator.LESS_THAN_EQUALS]: "Less Than or Equal",
    [ConditionOperator.IN]: "Is One Of",
    [ConditionOperator.NOT_IN]: "Is Not One Of",
    [ConditionOperator.CONTAINS]: "Contains",
    [ConditionOperator.NOT_CONTAINS]: "Does Not Contain",
    [ConditionOperator.STARTS_WITH]: "Starts With",
    [ConditionOperator.ENDS_WITH]: "Ends With",
    [ConditionOperator.BETWEEN]: "Between",
    [ConditionOperator.NOT_BETWEEN]: "Not Between"
  };
  return operatorMap[operator] || operator;
}

// Main BonusConditionBuilder component
function BonusConditionBuilder({
  conditions,
  onConditionsChange,
  allQuestions,
  form,
  className,
  readOnly
}: BonusConditionBuilderProps) {
  // Get repeatable sections for aggregation rules
  const repeatableSections = useMemo(() => {
    if (!form?.sections) return [];
    return form.sections.filter(section => section.repeatable);
  }, [form]);

  // Filter questions to include all types for bonus rules (more flexible than matching rules)
  const allowedQuestions = useMemo(() => {
    return allQuestions.filter(question =>
      // Allow all question types for bonus rules
      ['single_select', 'multi_select', 'boolean', 'number', 'short_text', 'long_text'].includes(question.type)
    );
  }, [allQuestions]);

  const addSimpleCondition = useCallback(() => {
    const newCondition: FilterCondition = {
      question_id: '',
      operator: ConditionOperator.EQUALS,
      value: []
    };
    onConditionsChange([...conditions, newCondition]);
  }, [conditions, onConditionsChange]);

  const addAggregationCondition = useCallback(() => {
    const newCondition: AggregationCondition = {
      question_id: '',
      operator: ConditionOperator.EQUALS,
      value: [],
      section_id: '',
      aggregation: AggregationType.ANY,
      aggregate_threshold: undefined
    };
    onConditionsChange([...conditions, newCondition]);
  }, [conditions, onConditionsChange]);

  const addCompoundCondition = useCallback(() => {
    const newCondition: CompoundFilter = {
      operator: LogicalOperator.AND,
      conditions: []
    };
    onConditionsChange([...conditions, newCondition]);
  }, [conditions, onConditionsChange]);

  const updateCondition = useCallback((index: number, updates: Record<string, any>) => {
    const newConditions = [...conditions];
    newConditions[index] = { ...newConditions[index], ...updates };
    onConditionsChange(newConditions);
  }, [conditions, onConditionsChange]);

  const removeCondition = useCallback((index: number) => {
    const newConditions = conditions.filter((_, i) => i !== index);
    onConditionsChange(newConditions);
  }, [conditions, onConditionsChange]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Add Condition Buttons */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={addSimpleCondition}
          disabled={allowedQuestions.length === 0 || readOnly}
        >
          <Plus className="h-4 w-4" />
          Add Condition
        </Button>

        {repeatableSections.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={addAggregationCondition}
            disabled={readOnly}
            className="border-blue-200 text-blue-700 hover:bg-blue-50"
          >
            <Plus className="h-4 w-4" />
            Add Repeatable Rule
          </Button>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={addCompoundCondition}
          disabled={allowedQuestions.length === 0 || readOnly}
        >
          <Plus className="h-4 w-4" />
          Add Group
        </Button>
      </div>

      {/* Conditions List */}
      <div className="space-y-3">
        <AnimatePresence mode="popLayout">
          {conditions.map((condition, index) => (
            <motion.div
              key={`condition-${index}`}
              layout
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {isSimpleCondition(condition) ? (
                isAggregationCondition(condition) ? (
                  <AggregationConditionBlock
                    condition={condition}
                    index={index}
                    onUpdate={(updates) => updateCondition(index, updates)}
                    onRemove={() => removeCondition(index)}
                    allQuestions={allowedQuestions}
                    form={form}
                    readOnly={readOnly}
                  />
                ) : (
                  <SimpleConditionCard
                    condition={condition}
                    index={index}
                    onUpdate={(updates) => updateCondition(index, updates)}
                    onRemove={() => removeCondition(index)}
                    allQuestions={allowedQuestions}
                    readOnly={readOnly}
                  />
                )
              ) : (
                <CompoundConditionCard
                  condition={condition}
                  index={index}
                  onUpdate={(updates) => updateCondition(index, updates)}
                  onRemove={() => removeCondition(index)}
                  allQuestions={allowedQuestions}
                  form={form}
                  readOnly={readOnly}
                />
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {conditions.length === 0 && (
        <div className="text-center py-8 text-muted-foreground border-2 border-dashed border-muted rounded-lg">
          <Plus className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="font-medium">No conditions defined</p>
          <p className="text-sm">Add conditions to define when this bonus rule should apply.</p>
        </div>
      )}
    </div>
  );
}

// Simple condition card component (reused from condition-builder but adapted for bonus rules)
interface SimpleConditionCardProps {
  condition: FilterCondition;
  index: number;
  onUpdate: (updates: Partial<FilterCondition>) => void;
  onRemove: () => void;
  allQuestions: QuestionWithSection[];
  readOnly?: boolean;
}

function SimpleConditionCard({
  condition,
  index,
  onUpdate,
  onRemove,
  allQuestions,
  readOnly
}: SimpleConditionCardProps) {
  const selectedQuestion = allQuestions.find(q =>
    (q._id || q.id) === condition.question_id
  );

  const availableOperators = selectedQuestion
    ? OPERATORS_BY_TYPE[selectedQuestion.type as keyof typeof OPERATORS_BY_TYPE] || []
    : [];

  return (
    <Card className="p-4 bg-background border">
      <div className="flex items-center gap-4">
        {/* Condition Index */}
        <div className="flex-shrink-0">
          <Badge variant="outline" className="text-xs">
            {index + 1}
          </Badge>
        </div>

        {/* Inputs: Question / Operator / Value */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 items-end">
          {/* Question */}
          <div className="flex flex-col gap-1">
            <Label className="text-xs font-medium text-muted-foreground">Question</Label>
            <Select
              value={condition.question_id}
              onValueChange={(value) => onUpdate({ question_id: value })}
              disabled={readOnly}
            >
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Select question..." />
              </SelectTrigger>
              <SelectContent>
                {allQuestions.map((question) => {
                  const questionId = question._id || question.id;
                  if (!questionId) return null;
                  return (
                    <SelectItem key={questionId} value={questionId}>
                      <div className="flex flex-col items-start">
                        <span className="font-medium">{question.label}</span>
                        <span className="text-xs text-muted-foreground">
                          {question.section_title}
                        </span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Operator */}
          <div className="flex flex-col gap-1">
            <Label className="text-xs font-medium text-muted-foreground">Operator</Label>
            <Select
              value={condition.operator || ''}
              onValueChange={(value: ConditionOperator) => onUpdate({ operator: value })}
              disabled={readOnly || !selectedQuestion}
            >
              <SelectTrigger className="h-9">
                <SelectValue placeholder="Select operator..." />
              </SelectTrigger>
              <SelectContent>
                {availableOperators.map((op) => (
                  <SelectItem key={op} value={op}>
                    {getOperatorDisplay(op)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Value */}
          <div className="flex flex-col gap-1">
            <Label className="text-xs font-medium text-muted-foreground">Value</Label>
            {selectedQuestion?.type === 'boolean' ? (
              <Select
                value={condition.value?.toString() || ''}
                onValueChange={(value) => onUpdate({ value: value === 'true' })}
                disabled={readOnly}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="Select value..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Yes</SelectItem>
                  <SelectItem value="false">No</SelectItem>
                </SelectContent>
              </Select>
            ) : selectedQuestion?.options ? (
              <Select
                value={condition.value || ''}
                onValueChange={(value) => onUpdate({ value })}
                disabled={readOnly}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="Select value..." />
                </SelectTrigger>
                <SelectContent>
                  {selectedQuestion.options.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Input
                type={selectedQuestion?.type === 'number' ? 'number' : 'text'}
                value={condition.value || ''}
                onChange={(e) => onUpdate({
                  value: selectedQuestion?.type === 'number' ? Number(e.target.value) : e.target.value
                })}
                placeholder="Enter value..."
                disabled={readOnly}
                className="h-9"
              />
            )}
          </div>
        </div>

        {/* Delete Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="text-destructive hover:text-destructive"
          disabled={readOnly}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </Card>
  );
}

// Compound condition card component
interface CompoundConditionCardProps {
  condition: CompoundFilter;
  index: number;
  onUpdate: (updates: Partial<CompoundFilter>) => void;
  onRemove: () => void;
  allQuestions: QuestionWithSection[];
  form?: Form;
  readOnly?: boolean;
}

function CompoundConditionCard({
  condition,
  index,
  onUpdate,
  onRemove,
  allQuestions,
  form,
  readOnly
}: CompoundConditionCardProps) {
  return (
    <Card className="p-3 bg-muted/50 border-l-4 border-l-purple-500">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="text-xs">
              Group
            </Badge>
            <Select
              value={condition.operator}
              onValueChange={(value: 'and' | 'or') => onUpdate({ operator: value as LogicalOperator })}
              disabled={readOnly}
            >
              <SelectTrigger className="w-32 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="and">AND</SelectItem>
                <SelectItem value="or">OR</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="text-destructive hover:text-destructive h-8 w-8 p-0"
            disabled={readOnly}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        {/* Nested Conditions */}
        <div className="ml-4 border-l-2 border-muted-foreground/20 pl-4">
          <BonusConditionBuilder
            conditions={condition.conditions}
            onConditionsChange={(conditions) => onUpdate({ conditions })}
            allQuestions={allQuestions}
            form={form}
            readOnly={readOnly}
          />
        </div>
      </div>
    </Card>
  );
}

export { BonusConditionBuilder, AggregationConditionBlock, type AggregationCondition };
